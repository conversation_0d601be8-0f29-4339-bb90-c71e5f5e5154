using System.Reflection;
using Bookify.Domain.Abstractions.Mediator;
using Microsoft.Extensions.DependencyInjection;

namespace Bookify.Application.Abstractions.Implementation;

/// <summary>
/// Extensions for configuring mediator services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds mediator services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration action</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddMediator(this IServiceCollection services, Action<MediatorConfiguration> configuration)
    {
        var config = new MediatorConfiguration();
        configuration(config);

        // Register mediator
        services.AddScoped<IMediator, Mediator>();
        services.AddScoped<ISender>(provider => provider.GetRequiredService<IMediator>());
        services.AddScoped<IPublisher>(provider => provider.GetRequiredService<IMediator>());

        // Register handlers from assemblies
        foreach (var assembly in config.Assemblies)
        {
            RegisterHandlers(services, assembly);
        }

        // Register behaviors
        foreach (var behaviorType in config.BehaviorTypes)
        {
            if (behaviorType.IsGenericTypeDefinition)
            {
                services.AddTransient(typeof(IPipelineBehavior<,>), behaviorType);
            }
            else
            {
                services.AddTransient(behaviorType);
            }
        }

        return services;
    }

    private static void RegisterHandlers(IServiceCollection services, Assembly assembly)
    {
        // Register request handlers
        var requestHandlerTypes = assembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract)
            .Where(t => t.GetInterfaces().Any(i => 
                i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IRequestHandler<,>)))
            .ToList();

        foreach (var handlerType in requestHandlerTypes)
        {
            var interfaceTypes = handlerType.GetInterfaces()
                .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IRequestHandler<,>));

            foreach (var interfaceType in interfaceTypes)
            {
                services.AddTransient(interfaceType, handlerType);
            }
        }

        // Register notification handlers
        var notificationHandlerTypes = assembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract)
            .Where(t => t.GetInterfaces().Any(i => 
                i.IsGenericType && i.GetGenericTypeDefinition() == typeof(INotificationHandler<>)))
            .ToList();

        foreach (var handlerType in notificationHandlerTypes)
        {
            var interfaceTypes = handlerType.GetInterfaces()
                .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(INotificationHandler<>));

            foreach (var interfaceType in interfaceTypes)
            {
                services.AddTransient(interfaceType, handlerType);
            }
        }
    }
}

/// <summary>
/// Configuration for mediator services
/// </summary>
public class MediatorConfiguration
{
    internal List<Assembly> Assemblies { get; } = new();
    internal List<Type> BehaviorTypes { get; } = new();

    /// <summary>
    /// Register services from assembly
    /// </summary>
    /// <param name="assembly">Assembly to scan</param>
    /// <returns>Configuration</returns>
    public MediatorConfiguration RegisterServicesFromAssembly(Assembly assembly)
    {
        Assemblies.Add(assembly);
        return this;
    }

    /// <summary>
    /// Add open behavior
    /// </summary>
    /// <param name="behaviorType">Behavior type</param>
    /// <returns>Configuration</returns>
    public MediatorConfiguration AddOpenBehavior(Type behaviorType)
    {
        BehaviorTypes.Add(behaviorType);
        return this;
    }
}
