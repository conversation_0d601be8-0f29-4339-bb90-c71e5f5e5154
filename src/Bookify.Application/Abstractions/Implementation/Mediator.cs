using Bookify.Domain.Abstractions.Mediator;
using Microsoft.Extensions.DependencyInjection;

namespace Bookify.Application.Abstractions.Implementation;

/// <summary>
/// Default mediator implementation
/// </summary>
internal sealed class Mediator : IMediator
{
    private readonly IServiceProvider _serviceProvider;

    public Mediator(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task<TResponse> Send<TResponse>(IRequest<TResponse> request, CancellationToken cancellationToken = default)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        var requestType = request.GetType();
        var responseType = typeof(TResponse);

        var handlerType = typeof(IRequestHandler<,>).MakeGenericType(requestType, responseType);
        var handler = _serviceProvider.GetService(handlerType);

        if (handler == null)
            throw new InvalidOperationException($"No handler registered for request type {requestType.Name}");

        // Get pipeline behaviors
        var behaviors = GetPipelineBehaviors(requestType, responseType);

        if (behaviors.Any())
        {
            // Execute through pipeline
            return await ExecutePipeline(request, behaviors, handler, cancellationToken);
        }

        // Execute handler directly if no behaviors
        var handleMethod = handlerType.GetMethod("Handle");
        var result = await (Task<TResponse>)handleMethod!.Invoke(handler, new object[] { request, cancellationToken })!;
        return result;
    }

    public async Task Publish<TNotification>(TNotification notification, CancellationToken cancellationToken = default) 
        where TNotification : INotification
    {
        if (notification == null)
            throw new ArgumentNullException(nameof(notification));

        var notificationType = notification.GetType();
        var handlerType = typeof(INotificationHandler<>).MakeGenericType(notificationType);
        
        var handlers = _serviceProvider.GetServices(handlerType);

        var tasks = handlers.Select(async handler =>
        {
            var handleMethod = handlerType.GetMethod("Handle");
            await (Task)handleMethod!.Invoke(handler, new object[] { notification, cancellationToken })!;
        });

        await Task.WhenAll(tasks);
    }

    private IEnumerable<object> GetPipelineBehaviors(Type requestType, Type responseType)
    {
        var behaviorType = typeof(IPipelineBehavior<,>).MakeGenericType(requestType, responseType);
        var behaviors = _serviceProvider.GetServices(behaviorType);

        // Also get behaviors that match the base request interface
        if (typeof(IBaseRequest).IsAssignableFrom(requestType))
        {
            var baseBehaviorType = typeof(IPipelineBehavior<,>);
            var allBehaviors = _serviceProvider.GetServices(baseBehaviorType);

            var matchingBehaviors = allBehaviors.Where(b =>
            {
                var behaviorInterfaceType = b.GetType().GetInterfaces()
                    .FirstOrDefault(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IPipelineBehavior<,>));

                if (behaviorInterfaceType == null) return false;

                var args = behaviorInterfaceType.GetGenericArguments();
                var behaviorRequestType = args[0];
                var behaviorResponseType = args[1];

                return behaviorRequestType.IsAssignableFrom(requestType) &&
                       behaviorResponseType.IsAssignableFrom(responseType);
            });

            behaviors = behaviors.Concat(matchingBehaviors);
        }

        return behaviors;
    }

    private async Task<TResponse> ExecutePipeline<TResponse>(
        object request, 
        IEnumerable<object> behaviors, 
        object handler, 
        CancellationToken cancellationToken)
    {
        var behaviorsList = behaviors.Reverse().ToList();
        
        RequestHandlerDelegate<TResponse> handlerDelegate = async () =>
        {
            var handlerType = handler.GetType();
            var handleMethod = handlerType.GetMethod("Handle");
            return await (Task<TResponse>)handleMethod!.Invoke(handler, new object[] { request, cancellationToken })!;
        };

        foreach (var behavior in behaviorsList)
        {
            var currentDelegate = handlerDelegate;
            var behaviorType = behavior.GetType();
            var handleMethod = behaviorType.GetMethod("Handle");
            
            handlerDelegate = async () =>
            {
                return await (Task<TResponse>)handleMethod!.Invoke(behavior, new object[] { request, currentDelegate, cancellationToken })!;
            };
        }

        return await handlerDelegate();
    }
}
